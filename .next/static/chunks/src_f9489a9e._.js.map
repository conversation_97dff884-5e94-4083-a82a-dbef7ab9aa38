{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/search.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst SearchIcon: FC = () => {\n\treturn (\n\t\t<Image src={'/search-icon.svg'} alt='Search' width={20} height={20} />\n\t)\n}\n\nexport default SearchIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,aAAiB;IACtB,qBACC,6LAAC,gIAAA,CAAA,UAAK;QAAC,KAAK;QAAoB,KAAI;QAAS,OAAO;QAAI,QAAQ;;;;;;AAElE;KAJM;uCAMS", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/input/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"input\": \"index-module__Bu1eGa__input\",\n  \"label\": \"index-module__Bu1eGa__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.tsx"], "sourcesContent": ["import { FC, HTMLInputTypeAttribute, ReactNode } from 'react'\nimport style from './index.module.css'\n\ninterface InputProps {\n\ttype?: HTMLInputTypeAttribute\n\tname?: string\n\tvalue?: string\n\tplaceholder?: string\n\tprefix?: ReactNode\n\tonChange?: (event: React.ChangeEvent<HTMLInputElement>) => void\n}\n\nconst Input: FC<InputProps> = ({\n\ttype,\n\tname,\n\tvalue,\n\tplaceholder,\n\tprefix,\n\tonChange\n}) => {\n\treturn (\n\t\t<label className={style.label}>\n\t\t\t{prefix}\n\t\t\t<input\n\t\t\t\tclassName={style.input}\n\t\t\t\ttype={type}\n\t\t\t\tname={name}\n\t\t\t\tvalue={value}\n\t\t\t\tplaceholder={placeholder}\n\t\t\t\tonChange={onChange}\n\t\t\t/>\n\t\t</label>\n\t)\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,QAAwB;QAAC,EAC9B,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,EACR;IACA,qBACC,6LAAC;QAAM,WAAW,oJAAA,CAAA,UAAK,CAAC,KAAK;;YAC3B;0BACD,6LAAC;gBACA,WAAW,oJAAA,CAAA,UAAK,CAAC,KAAK;gBACtB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;;;;;;;;;;;;AAId;KArBM;uCAuBS", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/model/searchStore.ts"], "sourcesContent": ["import { create } from 'zustand'\n\ninterface SearchStore {\n\tsearchQuery: string\n\tsetSearchQuery: (value: string) => void\n}\n\nexport const useSearchStore = create<SearchStore>(set => ({\n\tsearchQuery: '',\n\tsetSearchQuery: value => set({ searchQuery: value })\n}))\n"], "names": [], "mappings": ";;;AAAA;;AAOO,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAe,CAAA,MAAO,CAAC;QACzD,aAAa;QACb,gBAAgB,CAAA,QAAS,IAAI;gBAAE,aAAa;YAAM;IACnD,CAAC", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"notFound\": \"index-module__m10heG__notFound\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/searchDropdown.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\ninterface SearchDropdownProps {\n\tsearchQuery: string\n}\n\nconst SearchDropdown: FC<SearchDropdownProps> = ({ searchQuery }) => {\n\treturn (\n\t\t<div className={style.dropdown} data-is-visible={!!searchQuery.length}>\n\t\t\t<span className={style.notFound}>Ничего не найдено :(</span>\n\t\t</div>\n\t)\n}\n\nexport default SearchDropdown\n"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,iBAA0C;QAAC,EAAE,WAAW,EAAE;IAC/D,qBACC,6LAAC;QAAI,WAAW,oJAAA,CAAA,UAAK,CAAC,QAAQ;QAAE,mBAAiB,CAAC,CAAC,YAAY,MAAM;kBACpE,cAAA,6LAAC;YAAK,WAAW,oJAAA,CAAA,UAAK,CAAC,QAAQ;sBAAE;;;;;;;;;;;AAGpC;KANM;uCAQS", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx"], "sourcesContent": ["'use client'\n\nimport SearchIcon from '@/src/shared/ui/icons/search'\nimport Input from '@/src/shared/ui/input'\nimport { useSearchStore } from '../model/searchStore'\nimport style from './index.module.css'\nimport SearchDropdown from './searchDropdown'\n\nconst HeroSearch = () => {\n\tconst { searchQuery, setSearchQuery } = useSearchStore()\n\n\tconst handleSearch = (value: string) => {\n\t\tsetSearchQuery(value)\n\t\tconsole.log(value)\n\t}\n\n\treturn (\n\t\t<div className={style.search}>\n\t\t\t<Input\n\t\t\t\tplaceholder='Найти направление'\n\t\t\t\tprefix={<SearchIcon />}\n\t\t\t\tvalue={searchQuery}\n\t\t\t\tonChange={value => handleSearch(value.target.value)}\n\t\t\t/>\n\t\t\t<SearchDropdown searchQuery={searchQuery} />\n\t\t</div>\n\t)\n}\n\nexport default HeroSearch\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,aAAa;;IAClB,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAErD,MAAM,eAAe,CAAC;QACrB,eAAe;QACf,QAAQ,GAAG,CAAC;IACb;IAEA,qBACC,6LAAC;QAAI,WAAW,oJAAA,CAAA,UAAK,CAAC,MAAM;;0BAC3B,6LAAC,yIAAA,CAAA,UAAK;gBACL,aAAY;gBACZ,sBAAQ,6LAAC,0IAAA,CAAA,UAAU;;;;;gBACnB,OAAO;gBACP,UAAU,CAAA,QAAS,aAAa,MAAM,MAAM,CAAC,KAAK;;;;;;0BAEnD,6LAAC,kJAAA,CAAA,UAAc;gBAAC,aAAa;;;;;;;;;;;;AAGhC;GAnBM;;QACmC,iJAAA,CAAA,iBAAc;;;KADjD;uCAqBS", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/chevron.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst ChevronIcon: FC = () => {\n\treturn (\n\t\t<Image src={'/chevron-icon.svg'} alt='Chevron' width={7} height={12} />\n\t)\n}\n\nexport default ChevronIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,cAAkB;IACvB,qBACC,6LAAC,gIAAA,CAAA,UAAK;QAAC,KAAK;QAAqB,KAAI;QAAU,OAAO;QAAG,QAAQ;;;;;;AAEnE;KAJM;uCAMS", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/entities/country/ui/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__M6SB_q__card\",\n  \"content\": \"index-module__M6SB_q__content\",\n  \"flagWrapper\": \"index-module__M6SB_q__flagWrapper\",\n  \"info\": \"index-module__M6SB_q__info\",\n  \"price\": \"index-module__M6SB_q__price\",\n  \"title\": \"index-module__M6SB_q__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/entities/country/ui/card.tsx"], "sourcesContent": ["'use client'\n\nimport ChevronIcon from '@/src/shared/ui/icons/chevron'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { FC } from 'react'\nimport { CountryI } from '../model/country'\nimport style from './index.module.css'\n\ninterface CountryCardProps {\n    country: CountryI\n}\n\nconst CountryCard: FC<CountryCardProps> = ({ country }) => {\n    if (country.url) {\n        const href = country.url;\n        return (\n            <Link href={href} className={style.card}>\n                <div className={style.content}>\n                    <div className={style.flagWrapper}>\n                        <Image src={`/flags/${country.iso?.toLowerCase()}.svg`} alt={country.iso ?? 'country iso'} width={32} height={32} />\n                    </div>\n                    <div className={style.info}>\n                        <span className={style.title}>{country.country}</span>\n                        <span className={style.price}>\n                            {country.classic_info?.price_per_gb + ' ₽/ГБ'}\n                        </span>\n                    </div>\n                </div>\n                <ChevronIcon />\n            </Link>\n        )\n    } else {\n        return null\n    }\n}\n\nexport default CountryCard\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AAPA;;;;;;AAaA,MAAM,cAAoC;QAAC,EAAE,OAAO,EAAE;IAClD,IAAI,QAAQ,GAAG,EAAE;YAMyB,cAKjB;QAVrB,MAAM,OAAO,QAAQ,GAAG;YAKqD;QAJ7E,qBACI,6LAAC,+JAAA,CAAA,UAAI;YAAC,MAAM;YAAM,WAAW,wJAAA,CAAA,UAAK,CAAC,IAAI;;8BACnC,6LAAC;oBAAI,WAAW,wJAAA,CAAA,UAAK,CAAC,OAAO;;sCACzB,6LAAC;4BAAI,WAAW,wJAAA,CAAA,UAAK,CAAC,WAAW;sCAC7B,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAK,AAAC,UAAoC,QAA3B,eAAA,QAAQ,GAAG,cAAX,mCAAA,aAAa,WAAW,IAAG;gCAAO,KAAK,CAAA,gBAAA,QAAQ,GAAG,cAAX,2BAAA,gBAAe;gCAAe,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAElH,6LAAC;4BAAI,WAAW,wJAAA,CAAA,UAAK,CAAC,IAAI;;8CACtB,6LAAC;oCAAK,WAAW,wJAAA,CAAA,UAAK,CAAC,KAAK;8CAAG,QAAQ,OAAO;;;;;;8CAC9C,6LAAC;oCAAK,WAAW,wJAAA,CAAA,UAAK,CAAC,KAAK;8CACvB,EAAA,wBAAA,QAAQ,YAAY,cAApB,4CAAA,sBAAsB,YAAY,IAAG;;;;;;;;;;;;;;;;;;8BAIlD,6LAAC,2IAAA,CAAA,UAAW;;;;;;;;;;;IAGxB,OAAO;QACH,OAAO;IACX;AACJ;KAtBM;uCAwBS", "debugId": null}}]}