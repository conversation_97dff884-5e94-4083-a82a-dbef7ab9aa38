{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/pages/country/ui/index.module.css"], "sourcesContent": [".page {\n\tmin-height: 100vh;\n\tdisplay: flex;\n\tflex-direction: column;\n\tpadding: 0 0.5rem;\n}\n\n.main {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 0.5rem;\n\tpadding: 0.5rem 0;\n\n\t@media (min-width: 768px) {\n\t\tpadding: 3.25rem 0;\n\t\tgap: 1.5rem;\n\t}\n}\n\n.content {\n\twidth: 100%;\n\tmax-width: 720px;\n\tpadding: 4rem 0 0 0;\n\tmargin: 0 auto;\n}\n\n.countryHeader {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tgap: 0.5rem;\n\tpadding: 1rem;\n\tbackground: var(--surface);\n\tborder-radius: 1.5rem;\n}\n\n.flagWrapper {\n\twidth: 2rem;\n\theight: 2rem;\n\tborder-radius: 50%;\n\tbackground: var(--background);\n\toverflow: hidden;\n}\n\n.countryTitle {\n\tfont-size: 2rem;\n\tfont-weight: 600;\n\tmargin: 0 0 0.5rem 0;\n\tcolor: var(--text-primary);\n}\n\n.countryDescription {\n\tfont-size: 0.875rem;\n\tfont-weight: var(--font-body-weight);\n\tline-height: var(--font-body-line-height);\n\tletter-spacing: var(--font-body-letter-spacing);\n}"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;AAMC;EAA2B;;;;;;AAM5B;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;AAOA"}}]}