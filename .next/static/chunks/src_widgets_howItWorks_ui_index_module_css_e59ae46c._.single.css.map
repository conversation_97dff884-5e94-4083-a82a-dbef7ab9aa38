{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/howItWorks/ui/index.module.css"], "sourcesContent": [".card {\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground: var(--surface);\n\tborder-radius: 1.25rem;\n\n\toverflow: hidden;\n}\n\n.title {\n\tfont-size: var(--font-subtitle-size);\n\tfont-weight: var(--font-subtitle-weight);\n\tline-height: var(--font-subtitle-line-height);\n\tletter-spacing: var(--font-subtitle-letter-spacing);\n\tpadding: 1.5rem;\n}\n\n.steps {\n\tdisplay: flex;\n\tgap: 0.375rem;\n\twidth: 100%;\n\toverflow-x: auto;\n\toverflow-y: hidden;\n\tscrollbar-width: none;\n\t-ms-overflow-style: none;\n\tpadding: 0 1.5rem 1.5rem;\n\tcursor: grab;\n\tuser-select: none;\n\t-webkit-user-select: none;\n\t-moz-user-select: none;\n\t-ms-user-select: none;\n\tscroll-behavior: smooth;\n}\n\n.steps::-webkit-scrollbar {\n\tdisplay: none;\n}\n\n.steps.dragging {\n\tcursor: grabbing;\n\tscroll-behavior: auto;\n}\n\n.step {\n\twidth: 15.625rem;\n\tmin-width: 15.625rem;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: space-between;\n\talign-items: center;\n\tgap: 0.5rem;\n\tbackground: var(--background);\n\tborder-radius: 1.25rem;\n\tpointer-events: none;\n}\n\n.steps:not(.dragging) .step {\n\tpointer-events: auto;\n}\n\n.stepTitle {\n\tfont-size: var(--font-body-size);\n\tfont-weight: 600;\n\tline-height: var(--font-body-line-height);\n\tletter-spacing: var(--font-body-letter-spacing);\n\ttext-align: center;\n\tpadding: 1rem 1.5rem;\n}\n\n.stepImage {\n\twidth: 10.125rem;\n\theight: 15.375rem;\n\tborder-radius: 1.5rem 1.5rem 0 0;\n\tbackground: #D9D9D9;\n}"], "names": [], "mappings": "AAAA;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;AAIA;;;;;;;;;AASA"}}]}