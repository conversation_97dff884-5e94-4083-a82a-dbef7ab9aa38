/* [project]/src/shared/ui/logo/index.module.css [app-client] (css) */
.index-module__ytjyqG__logo {
  align-items: center;
  gap: .5rem;
  display: flex;
}

/* [project]/src/widgets/header/ui/index.module.css [app-client] (css) */
.index-module__6gvmdq__header {
  background: var(--surface);
  width: 100%;
  height: 4rem;
  box-shadow: 0 1px 0 var(--shadow);
  z-index: 100;
  justify-content: space-between;
  align-items: center;
  padding: .625rem 1rem;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
}

/* [project]/src/widgets/hero/ui/index.module.css [app-client] (css) */
.index-module__m10heG__hero {
  flex-direction: column;
  gap: 2rem;
  padding: 1.25rem 0 1rem;
  display: flex;
}

.index-module__m10heG__title {
  font-size: var(--font-title-size);
  font-weight: var(--font-title-weight);
  line-height: var(--font-title-line-height);
  letter-spacing: var(--font-title-letter-spacing);
  text-align: center;
  text-wrap: balance;
}

.index-module__m10heG__search {
  position: relative;
}

.index-module__m10heG__dropdown[data-is-visible="false"] {
  opacity: 0;
  pointer-events: none;
}

.index-module__m10heG__dropdown {
  opacity: 1;
  background: var(--surface);
  border-radius: 1.25rem;
  flex-direction: column;
  width: 100%;
  min-height: 8.125rem;
  max-height: 18.75rem;
  transition: opacity .2s;
  display: flex;
  position: absolute;
  top: calc(100% + .375rem);
  left: 0;
  box-shadow: 0 .5rem .625rem .25rem rgba(0, 0, 0, .1);
}

.index-module__m10heG__notFound {
  margin: auto;
}

/* [project]/src/shared/ui/input/index.module.css [app-client] (css) */
.index-module__Bu1eGa__label {
  background: var(--input-surface);
  cursor: text;
  border-radius: 1rem;
  align-items: center;
  gap: .5rem;
  padding: .75rem;
  transition: background .2s;
  display: flex;
}

.index-module__Bu1eGa__label:focus-within {
  background: var(--input-surface-focus);
}

.index-module__Bu1eGa__input {
  font-size: var(--font-body-size);
  font-weight: var(--font-body-weight);
  line-height: var(--font-body-line-height);
  letter-spacing: var(--font-body-letter-spacing);
  color: var(--foreground);
}

/* [project]/src/widgets/howItWorks/ui/index.module.css [app-client] (css) */
.index-module__aAZmyW__card {
  background: var(--surface);
  border-radius: 1.25rem;
  flex-direction: column;
  display: flex;
  overflow: hidden;
}

.index-module__aAZmyW__title {
  font-size: var(--font-subtitle-size);
  font-weight: var(--font-subtitle-weight);
  line-height: var(--font-subtitle-line-height);
  letter-spacing: var(--font-subtitle-letter-spacing);
  padding: 1.5rem;
}

.index-module__aAZmyW__steps {
  scrollbar-width: none;
  -ms-overflow-style: none;
  cursor: grab;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  scroll-behavior: smooth;
  gap: .375rem;
  width: 100%;
  padding: 0 1.5rem 1.5rem;
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
}

.index-module__aAZmyW__steps::-webkit-scrollbar {
  display: none;
}

.index-module__aAZmyW__steps.index-module__aAZmyW__dragging {
  cursor: grabbing;
  scroll-behavior: auto;
}

.index-module__aAZmyW__step {
  background: var(--background);
  pointer-events: none;
  border-radius: 1.25rem;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  gap: .5rem;
  width: 15.625rem;
  min-width: 15.625rem;
  display: flex;
}

.index-module__aAZmyW__steps:not(.index-module__aAZmyW__dragging) .index-module__aAZmyW__step {
  pointer-events: auto;
}

.index-module__aAZmyW__stepTitle {
  font-size: var(--font-body-size);
  font-weight: 600;
  line-height: var(--font-body-line-height);
  letter-spacing: var(--font-body-letter-spacing);
  text-align: center;
  padding: 1rem 1.5rem;
}

.index-module__aAZmyW__stepImage {
  background: #d9d9d9;
  border-radius: 1.5rem 1.5rem 0 0;
  width: 10.125rem;
  height: 15.375rem;
}

/* [project]/src/widgets/main/ui/index.module.css [app-client] (css) */
.index-module__aQ9zUq__main {
  flex-direction: column;
  gap: .5rem;
  padding: .5rem 0 1.5rem;
  display: flex;
}

.index-module__aQ9zUq__card {
  background: var(--surface);
  border-radius: 1.25rem;
  flex-direction: column;
  display: flex;
}

.index-module__aQ9zUq__title {
  font-size: var(--font-subtitle-size);
  font-weight: var(--font-subtitle-weight);
  line-height: var(--font-subtitle-line-height);
  letter-spacing: var(--font-subtitle-letter-spacing);
  padding: 1rem 1rem .75rem;
}

.index-module__aQ9zUq__loadMore {
  padding: .75rem 1rem 1rem;
}

.index-module__aQ9zUq__popularCountries, .index-module__aQ9zUq__countries {
  flex-direction: column;
  display: flex;
}

/* [project]/src/entities/country/ui/index.module.css [app-client] (css) */
.index-module__M6SB_q__card {
  background: var(--surface);
  border-radius: 1.25rem;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  width: 100%;
  height: 3.625rem;
  padding: 0 1rem;
  display: flex;
}

.index-module__M6SB_q__content {
  align-items: center;
  gap: 1rem;
  display: flex;
}

.index-module__M6SB_q__flagWrapper {
  background: var(--background);
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  overflow: hidden;
}

.index-module__M6SB_q__info {
  flex-direction: column;
  align-items: flex-start;
  gap: .125rem;
  display: flex;
}

.index-module__M6SB_q__title {
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.125rem;
}

.index-module__M6SB_q__price {
  font-size: var(--font-caption-size);
  font-weight: var(--font-caption-weight);
  line-height: var(--font-caption-line-height);
  letter-spacing: var(--font-caption-letter-spacing);
}

/* [project]/src/shared/ui/buttons/textButton/index.module.css [app-client] (css) */
.index-module__mWR7qa__button {
  font-size: var(--font-body-size);
  font-weight: 600;
  line-height: var(--font-body-line-height);
  letter-spacing: var(--font-body-letter-spacing);
  color: var(--surface);
  background: var(--button-surface);
  border-radius: 1rem;
  width: 100%;
  height: 3.25rem;
  transition: background .2s;
}

.index-module__mWR7qa__button:hover {
  background: var(--button-surface-hover);
}

/* [project]/src/pages/home/<USER>/index.module.css [app-client] (css) */
.index-module__vhjfEW__page {
  flex-direction: column;
  min-height: 100vh;
  display: flex;
}

.index-module__vhjfEW__content {
  width: 100%;
  max-width: 720px;
  margin: 0 auto;
  padding: 4rem .5rem 0;
}

/*# sourceMappingURL=src_246a884d._.css.map*/