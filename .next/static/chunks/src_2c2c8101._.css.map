{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.module.css"], "sourcesContent": [".logo {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 0.5rem;\n}\n"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 8, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.module.css"], "sourcesContent": [".header {\n\twidth: 100%;\n\theight: 4rem;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 0.625rem 1rem;\n\tbackground: var(--surface);\n\tbox-shadow: 0 1px 0 var(--shadow);\n\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tz-index: 100;\n}\n"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/howItWorks/ui/index.module.css"], "sourcesContent": [".card {\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground: var(--surface);\n\tborder-radius: 1.25rem;\n\n\toverflow: hidden;\n}\n\n.title {\n\tfont-size: var(--font-subtitle-size);\n\tfont-weight: var(--font-subtitle-weight);\n\tline-height: var(--font-subtitle-line-height);\n\tletter-spacing: var(--font-subtitle-letter-spacing);\n\tpadding: 1.5rem;\n}\n\n.steps {\n\tdisplay: flex;\n\tgap: 0.375rem;\n\twidth: 100%;\n\toverflow-x: auto;\n\toverflow-y: hidden;\n\tscrollbar-width: none;\n\t-ms-overflow-style: none;\n\tpadding: 0 1.5rem 1.5rem;\n\tcursor: grab;\n\tuser-select: none;\n\t-webkit-user-select: none;\n\t-moz-user-select: none;\n\t-ms-user-select: none;\n\tscroll-behavior: smooth;\n}\n\n.steps::-webkit-scrollbar {\n\tdisplay: none;\n}\n\n.steps.dragging {\n\tcursor: grabbing;\n\tscroll-behavior: auto;\n}\n\n.step {\n\twidth: 15.625rem;\n\tmin-width: 15.625rem;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: space-between;\n\talign-items: center;\n\tgap: 0.5rem;\n\tbackground: var(--background);\n\tborder-radius: 1.25rem;\n\tpointer-events: none;\n}\n\n.steps:not(.dragging) .step {\n\tpointer-events: auto;\n}\n\n.stepTitle {\n\tfont-size: var(--font-body-size);\n\tfont-weight: 600;\n\tline-height: var(--font-body-line-height);\n\tletter-spacing: var(--font-body-letter-spacing);\n\ttext-align: center;\n\tpadding: 1rem 1.5rem;\n}\n\n.stepImage {\n\twidth: 10.125rem;\n\theight: 15.375rem;\n\tborder-radius: 1.5rem 1.5rem 0 0;\n\tbackground: #D9D9D9;\n}"], "names": [], "mappings": "AAAA;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;AAIA;;;;;;;;;AASA", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/country/ui/index.module.css"], "sourcesContent": [".page {\n\tmin-height: 100vh;\n\tdisplay: flex;\n\tflex-direction: column;\n\tpadding: 0 0.5rem;\n}\n\n.main {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 0.5rem;\n\tpadding: 0.5rem 0;\n\n\t@media (min-width: 768px) {\n\t\tpadding: 3.25rem 0;\n\t\tgap: 1.5rem;\n\t}\n}\n\n.content {\n\twidth: 100%;\n\tmax-width: 720px;\n\tpadding: 4rem 0 0 0;\n\tmargin: 0 auto;\n}\n\n.countryHeader {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tgap: 0.5rem;\n\tpadding: 1rem;\n\tbackground: var(--surface);\n\tborder-radius: 1.5rem;\n}\n\n.flagWrapper {\n\twidth: 2rem;\n\theight: 2rem;\n\tborder-radius: 50%;\n\tbackground: var(--background);\n\toverflow: hidden;\n}\n\n.countryTitle {\n\tfont-size: 2rem;\n\tfont-weight: 600;\n\tmargin: 0 0 0.5rem 0;\n\tcolor: var(--text-primary);\n}\n\n.countryDescription {\n\tfont-size: 0.875rem;\n\tfont-weight: var(--font-body-weight);\n\tline-height: var(--font-body-line-height);\n\tletter-spacing: var(--font-body-letter-spacing);\n}"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;AAMC;EAA2B;;;;;;AAM5B;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;AAOA", "debugId": null}}]}