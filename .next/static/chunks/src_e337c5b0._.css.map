{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.module.css"], "sourcesContent": [".logo {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 0.5rem;\n}\n"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 8, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.module.css"], "sourcesContent": [".header {\n\twidth: 100%;\n\theight: 4rem;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 0.625rem 1rem;\n\tbackground: var(--surface);\n\tbox-shadow: 0 1px 0 var(--shadow);\n\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tz-index: 100;\n}\n"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/country/ui/index.module.css"], "sourcesContent": [".page {\n\tmin-height: 100vh;\n\tdisplay: flex;\n\tflex-direction: column;\n\tpadding: 0 0.5rem;\n}\n\n.content {\n\twidth: 100%;\n\tmax-width: 720px;\n\tpadding: 4rem 0 0 0;\n\tmargin: 0 auto;\n}\n\n.countryHeader {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tgap: 0.5rem;\n\tpadding: 1rem;\n\tbackground: var(--surface);\n\tborder-radius: 1.5rem;\n}\n\n.flagWrapper {\n\twidth: 2rem;\n\theight: 2rem;\n\tborder-radius: 50%;\n\tbackground: var(--background);\n\toverflow: hidden;\n}\n\n.countryTitle {\n\tfont-size: 2rem;\n\tfont-weight: 600;\n\tmargin: 0 0 0.5rem 0;\n\tcolor: var(--text-primary);\n}\n\n.countryDescription {\n\tfont-size: 0.875rem;\n\tfont-weight: var(--font-body-weight);\n\tline-height: var(--font-body-line-height);\n\tletter-spacing: var(--font-body-letter-spacing);\n}"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;AAOA", "debugId": null}}]}