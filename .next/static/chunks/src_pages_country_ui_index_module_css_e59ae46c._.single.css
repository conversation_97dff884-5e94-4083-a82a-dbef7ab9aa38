/* [project]/src/pages/country/ui/index.module.css [app-client] (css) */
.index-module__C7fkEW__page {
  flex-direction: column;
  min-height: 100vh;
  padding: 0 .5rem;
  display: flex;
}

.index-module__C7fkEW__main {
  flex-direction: column;
  gap: .5rem;
  padding: .5rem 0;
  display: flex;
}

@media (min-width: 768px) {
  .index-module__C7fkEW__main {
    gap: 1.5rem;
    padding: 3.25rem 0;
  }
}

.index-module__C7fkEW__content {
  width: 100%;
  max-width: 720px;
  margin: 0 auto;
  padding: 4rem 0 0;
}

.index-module__C7fkEW__countryHeader {
  background: var(--surface);
  border-radius: 1.5rem;
  justify-content: space-between;
  align-items: center;
  gap: .5rem;
  padding: 1rem;
  display: flex;
}

.index-module__C7fkEW__flagWrapper {
  background: var(--background);
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  overflow: hidden;
}

.index-module__C7fkEW__countryTitle {
  color: var(--text-primary);
  margin: 0 0 .5rem;
  font-size: 2rem;
  font-weight: 600;
}

.index-module__C7fkEW__countryDescription {
  font-size: .875rem;
  font-weight: var(--font-body-weight);
  line-height: var(--font-body-line-height);
  letter-spacing: var(--font-body-letter-spacing);
}

/*# sourceMappingURL=src_pages_country_ui_index_module_css_e59ae46c._.single.css.map*/