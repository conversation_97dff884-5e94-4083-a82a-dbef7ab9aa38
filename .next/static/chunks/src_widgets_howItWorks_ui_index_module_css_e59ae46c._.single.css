/* [project]/src/widgets/howItWorks/ui/index.module.css [app-client] (css) */
.index-module__aAZmyW__card {
  background: var(--surface);
  border-radius: 1.25rem;
  flex-direction: column;
  display: flex;
  overflow: hidden;
}

.index-module__aAZmyW__title {
  font-size: var(--font-subtitle-size);
  font-weight: var(--font-subtitle-weight);
  line-height: var(--font-subtitle-line-height);
  letter-spacing: var(--font-subtitle-letter-spacing);
  padding: 1.5rem;
}

.index-module__aAZmyW__steps {
  scrollbar-width: none;
  -ms-overflow-style: none;
  cursor: grab;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  scroll-behavior: smooth;
  gap: .375rem;
  width: 100%;
  padding: 0 1.5rem 1.5rem;
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
}

.index-module__aAZmyW__steps::-webkit-scrollbar {
  display: none;
}

.index-module__aAZmyW__steps.index-module__aAZmyW__dragging {
  cursor: grabbing;
  scroll-behavior: auto;
}

.index-module__aAZmyW__step {
  background: var(--background);
  pointer-events: none;
  border-radius: 1.25rem;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  gap: .5rem;
  width: 15.625rem;
  min-width: 15.625rem;
  display: flex;
}

.index-module__aAZmyW__steps:not(.index-module__aAZmyW__dragging) .index-module__aAZmyW__step {
  pointer-events: auto;
}

.index-module__aAZmyW__stepTitle {
  font-size: var(--font-body-size);
  font-weight: 600;
  line-height: var(--font-body-line-height);
  letter-spacing: var(--font-body-letter-spacing);
  text-align: center;
  padding: 1rem 1.5rem;
}

.index-module__aAZmyW__stepImage {
  background: #d9d9d9;
  border-radius: 1.5rem 1.5rem 0 0;
  width: 10.125rem;
  height: 15.375rem;
}

/*# sourceMappingURL=src_widgets_howItWorks_ui_index_module_css_e59ae46c._.single.css.map*/