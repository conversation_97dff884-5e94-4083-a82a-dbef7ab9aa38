/* [project]/src/shared/ui/logo/index.module.css [app-client] (css) */
.index-module__ytjyqG__logo {
  align-items: center;
  gap: .5rem;
  display: flex;
}

/* [project]/src/widgets/header/ui/index.module.css [app-client] (css) */
.index-module__6gvmdq__header {
  background: var(--surface);
  width: 100%;
  height: 4rem;
  box-shadow: 0 1px 0 var(--shadow);
  z-index: 100;
  justify-content: space-between;
  align-items: center;
  padding: .625rem 1rem;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
}

/* [project]/src/pages/country/ui/index.module.css [app-client] (css) */
.index-module__C7fkEW__page {
  flex-direction: column;
  min-height: 100vh;
  padding: 0 .5rem;
  display: flex;
}

.index-module__C7fkEW__content {
  width: 100%;
  max-width: 720px;
  margin: 0 auto;
  padding: 4rem 0 0;
}

.index-module__C7fkEW__countryHeader {
  background: var(--surface);
  border-radius: 1.5rem;
  justify-content: space-between;
  align-items: center;
  gap: .5rem;
  padding: 1rem;
  display: flex;
}

.index-module__C7fkEW__flagWrapper {
  background: var(--background);
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  overflow: hidden;
}

.index-module__C7fkEW__countryTitle {
  color: var(--text-primary);
  margin: 0 0 .5rem;
  font-size: 2rem;
  font-weight: 600;
}

.index-module__C7fkEW__countryDescription {
  font-size: .875rem;
  font-weight: var(--font-body-weight);
  line-height: var(--font-body-line-height);
  letter-spacing: var(--font-body-letter-spacing);
}

/*# sourceMappingURL=src_e337c5b0._.css.map*/