{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/howItWorks/model/steps.ts"], "sourcesContent": ["interface StepI {\n    title: string\n    description?: string\n    image?: string\n}\n\nexport const steps: StepI[] = [\n    {\n        title: 'Очень длинный заголовок',\n    },\n    {\n        title: 'Определяем страну',\n        description:\n            'Вам нужно определить страну, в которой вы находитесь. Это можно сделать, выбрав страну из списка или введя ее в поиске.',\n    },\n    {\n        title: 'Выбираем тариф',\n        description:\n            'Выберите подходящий тариф. Мы предлагаем разные варианты, включая тарифы с ежедневной оплатой и тарифы с оплатой GB.',\n    },\n    {\n        title: 'Оплачиваим',\n        description:\n            'После выбора тарифа, перейдите к оплате. Мы принимаем платежи через банковскую карту, Apple Pay и Google Pay.',\n    },\n    {\n        title: 'Получаем eSIM',\n        description: 'После оплаты, вы получите eSIM.',\n    },\n]\n"], "names": [], "mappings": ";;;AAMO,MAAM,QAAiB;IAC1B;QACI,OAAO;IACX;IACA;QACI,OAAO;QACP,aACI;IACR;IACA;QACI,OAAO;QACP,aACI;IACR;IACA;QACI,OAAO;QACP,aACI;IACR;IACA;QACI,OAAO;QACP,aAAa;IACjB;CACH", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/howItWorks/ui/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aAZmyW__card\",\n  \"dragging\": \"index-module__aAZmyW__dragging\",\n  \"step\": \"index-module__aAZmyW__step\",\n  \"stepImage\": \"index-module__aAZmyW__stepImage\",\n  \"stepTitle\": \"index-module__aAZmyW__stepTitle\",\n  \"steps\": \"index-module__aAZmyW__steps\",\n  \"title\": \"index-module__aAZmyW__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/howItWorks/ui/index.tsx"], "sourcesContent": ["'use client'\n\nimport { FC, useCallback, useRef, useState } from 'react'\nimport { steps } from '../model/steps'\nimport style from './index.module.css'\n\nconst HowItsWork: FC = () => {\n\tconst scrollContainerRef = useRef<HTMLDivElement>(null)\n\tconst [isDragging, setIsDragging] = useState(false)\n\tconst [startX, setStartX] = useState(0)\n\tconst [scrollLeft, setScrollLeft] = useState(0)\n\n\t// For mouse\n\t// click\n\tconst handleMouseDown = useCallback((e: React.MouseEvent) => {\n\t\tif (!scrollContainerRef.current) return\n\n\t\tsetIsDragging(true)\n\t\tsetStartX(e.pageX - scrollContainerRef.current.offsetLeft)\n\t\tsetScrollLeft(scrollContainerRef.current.scrollLeft)\n\n\t\te.preventDefault()\n\t}, [])\n\n\t// move\n\tconst handleMouseMove = useCallback((e: React.MouseEvent) => {\n\t\tif (!isDragging || !scrollContainerRef.current) return\n\n\t\te.preventDefault()\n\t\tconst x = e.pageX - scrollContainerRef.current.offsetLeft\n\t\tconst walk = (x - startX) * 1.2 // Множитель для скорости прокрутки\n\t\tscrollContainerRef.current.scrollLeft = scrollLeft - walk\n\t}, [isDragging, startX, scrollLeft])\n\n\tconst handleMouseUp = useCallback(() => {\n\t\tsetIsDragging(false)\n\t}, [])\n\n\tconst handleMouseLeave = useCallback(() => {\n\t\tsetIsDragging(false)\n\t}, [])\n\n\t// For mobile\n\t// touch\n\tconst handleTouchStart = useCallback((e: React.TouchEvent) => {\n\t\tif (!scrollContainerRef.current) return\n\n\t\tsetIsDragging(true)\n\t\tsetStartX(e.touches[0].pageX - scrollContainerRef.current.offsetLeft)\n\t\tsetScrollLeft(scrollContainerRef.current.scrollLeft)\n\t}, [])\n\n\t// move\n\tconst handleTouchMove = useCallback((e: React.TouchEvent) => {\n\t\tif (!isDragging || !scrollContainerRef.current) return\n\n\t\tconst x = e.touches[0].pageX - scrollContainerRef.current.offsetLeft\n\t\tconst walk = (x - startX) * 1\n\t\tscrollContainerRef.current.scrollLeft = scrollLeft - walk\n\t}, [isDragging, startX, scrollLeft])\n\n\tconst handleTouchEnd = useCallback(() => {\n\t\tsetIsDragging(false)\n\t}, [])\n\n\treturn (\n\t\t<div className={style.card}>\n\t\t\t<h2 className={style.title}>Как это работает</h2>\n\t\t\t<div\n\t\t\t\tref={scrollContainerRef}\n\t\t\t\tclassName={`${style.steps} ${isDragging ? style.dragging : ''}`}\n\t\t\t\tonMouseDown={handleMouseDown}\n\t\t\t\tonMouseMove={handleMouseMove}\n\t\t\t\tonMouseUp={handleMouseUp}\n\t\t\t\tonMouseLeave={handleMouseLeave}\n\t\t\t\tonTouchStart={handleTouchStart}\n\t\t\t\tonTouchMove={handleTouchMove}\n\t\t\t\tonTouchEnd={handleTouchEnd}\n\t\t\t>\n\t\t\t\t{steps.map((step) => {\n\t\t\t\t\treturn (\n\t\t\t\t\t\t<div key={step.title} className={style.step}>\n\t\t\t\t\t\t\t<h3 className={style.stepTitle}>\n\t\t\t\t\t\t\t\t{step.title}\n\t\t\t\t\t\t\t</h3>\n\t\t\t\t\t\t\t<div className={style.stepImage} />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t)\n\t\t\t\t})}\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default HowItsWork\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,aAAiB;;IACtB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,YAAY;IACZ,QAAQ;IACR,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACpC,IAAI,CAAC,mBAAmB,OAAO,EAAE;YAEjC,cAAc;YACd,UAAU,EAAE,KAAK,GAAG,mBAAmB,OAAO,CAAC,UAAU;YACzD,cAAc,mBAAmB,OAAO,CAAC,UAAU;YAEnD,EAAE,cAAc;QACjB;kDAAG,EAAE;IAEL,OAAO;IACP,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACpC,IAAI,CAAC,cAAc,CAAC,mBAAmB,OAAO,EAAE;YAEhD,EAAE,cAAc;YAChB,MAAM,IAAI,EAAE,KAAK,GAAG,mBAAmB,OAAO,CAAC,UAAU;YACzD,MAAM,OAAO,CAAC,IAAI,MAAM,IAAI,IAAI,mCAAmC;;YACnE,mBAAmB,OAAO,CAAC,UAAU,GAAG,aAAa;QACtD;kDAAG;QAAC;QAAY;QAAQ;KAAW;IAEnC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACjC,cAAc;QACf;gDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YACpC,cAAc;QACf;mDAAG,EAAE;IAEL,aAAa;IACb,QAAQ;IACR,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YACrC,IAAI,CAAC,mBAAmB,OAAO,EAAE;YAEjC,cAAc;YACd,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG,mBAAmB,OAAO,CAAC,UAAU;YACpE,cAAc,mBAAmB,OAAO,CAAC,UAAU;QACpD;mDAAG,EAAE;IAEL,OAAO;IACP,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACpC,IAAI,CAAC,cAAc,CAAC,mBAAmB,OAAO,EAAE;YAEhD,MAAM,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG,mBAAmB,OAAO,CAAC,UAAU;YACpE,MAAM,OAAO,CAAC,IAAI,MAAM,IAAI;YAC5B,mBAAmB,OAAO,CAAC,UAAU,GAAG,aAAa;QACtD;kDAAG;QAAC;QAAY;QAAQ;KAAW;IAEnC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAClC,cAAc;QACf;iDAAG,EAAE;IAEL,qBACC,6LAAC;QAAI,WAAW,0JAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,6LAAC;gBAAG,WAAW,0JAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAC5B,6LAAC;gBACA,KAAK;gBACL,WAAW,AAAC,GAAiB,OAAf,0JAAA,CAAA,UAAK,CAAC,KAAK,EAAC,KAAoC,OAAjC,aAAa,0JAAA,CAAA,UAAK,CAAC,QAAQ,GAAG;gBAC3D,aAAa;gBACb,aAAa;gBACb,WAAW;gBACX,cAAc;gBACd,cAAc;gBACd,aAAa;gBACb,YAAY;0BAEX,iJAAA,CAAA,QAAK,CAAC,GAAG,CAAC,CAAC;oBACX,qBACC,6LAAC;wBAAqB,WAAW,0JAAA,CAAA,UAAK,CAAC,IAAI;;0CAC1C,6LAAC;gCAAG,WAAW,0JAAA,CAAA,UAAK,CAAC,SAAS;0CAC5B,KAAK,KAAK;;;;;;0CAEZ,6LAAC;gCAAI,WAAW,0JAAA,CAAA,UAAK,CAAC,SAAS;;;;;;;uBAJtB,KAAK,KAAK;;;;;gBAOtB;;;;;;;;;;;;AAIJ;GAtFM;KAAA;uCAwFS", "debugId": null}}]}