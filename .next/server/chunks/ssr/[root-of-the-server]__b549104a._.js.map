{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/search.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst SearchIcon: FC = () => {\n\treturn (\n\t\t<Image src={'/search-icon.svg'} alt='Search' width={20} height={20} />\n\t)\n}\n\nexport default SearchIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,aAAiB;IACtB,qBACC,8OAAC,6HAAA,CAAA,UAAK;QAAC,KAAK;QAAoB,KAAI;QAAS,OAAO;QAAI,QAAQ;;;;;;AAElE;uCAEe", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/input/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"input\": \"index-module__Bu1eGa__input\",\n  \"label\": \"index-module__Bu1eGa__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/input/index.tsx"], "sourcesContent": ["import { FC, HTMLInputTypeAttribute, ReactNode } from 'react'\nimport style from './index.module.css'\n\ninterface InputProps {\n\ttype?: HTMLInputTypeAttribute\n\tname?: string\n\tvalue?: string\n\tplaceholder?: string\n\tprefix?: ReactNode\n\tonChange?: (event: React.ChangeEvent<HTMLInputElement>) => void\n}\n\nconst Input: FC<InputProps> = ({\n\ttype,\n\tname,\n\tvalue,\n\tplaceholder,\n\tprefix,\n\tonChange\n}) => {\n\treturn (\n\t\t<label className={style.label}>\n\t\t\t{prefix}\n\t\t\t<input\n\t\t\t\tclassName={style.input}\n\t\t\t\ttype={type}\n\t\t\t\tname={name}\n\t\t\t\tvalue={value}\n\t\t\t\tplaceholder={placeholder}\n\t\t\t\tonChange={onChange}\n\t\t\t/>\n\t\t</label>\n\t)\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,QAAwB,CAAC,EAC9B,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,EACR;IACA,qBACC,8OAAC;QAAM,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;;YAC3B;0BACD,8OAAC;gBACA,WAAW,iJAAA,CAAA,UAAK,CAAC,KAAK;gBACtB,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;;;;;;;;;;;;AAId;uCAEe", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/model/searchStore.ts"], "sourcesContent": ["import { create } from 'zustand'\n\ninterface SearchStore {\n\tsearchQuery: string\n\tsetSearchQuery: (value: string) => void\n}\n\nexport const useSearchStore = create<SearchStore>(set => ({\n\tsearchQuery: '',\n\tsetSearchQuery: value => set({ searchQuery: value })\n}))\n"], "names": [], "mappings": ";;;AAAA;;AAOO,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAe,CAAA,MAAO,CAAC;QACzD,aAAa;QACb,gBAAgB,CAAA,QAAS,IAAI;gBAAE,aAAa;YAAM;IACnD,CAAC", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/hero/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"dropdown\": \"index-module__m10heG__dropdown\",\n  \"hero\": \"index-module__m10heG__hero\",\n  \"notFound\": \"index-module__m10heG__notFound\",\n  \"search\": \"index-module__m10heG__search\",\n  \"title\": \"index-module__m10heG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/searchDropdown.tsx"], "sourcesContent": ["import { FC } from 'react'\nimport style from './index.module.css'\n\ninterface SearchDropdownProps {\n\tsearchQuery: string\n}\n\nconst SearchDropdown: FC<SearchDropdownProps> = ({ searchQuery }) => {\n\treturn (\n\t\t<div className={style.dropdown} data-is-visible={!!searchQuery.length}>\n\t\t\t<span className={style.notFound}>Ничего не найдено :(</span>\n\t\t</div>\n\t)\n}\n\nexport default SearchDropdown\n"], "names": [], "mappings": ";;;;AACA;;;AAMA,MAAM,iBAA0C,CAAC,EAAE,WAAW,EAAE;IAC/D,qBACC,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;QAAE,mBAAiB,CAAC,CAAC,YAAY,MAAM;kBACpE,cAAA,8OAAC;YAAK,WAAW,iJAAA,CAAA,UAAK,CAAC,QAAQ;sBAAE;;;;;;;;;;;AAGpC;uCAEe", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/hero/ui/search.tsx"], "sourcesContent": ["'use client'\n\nimport SearchIcon from '@/src/shared/ui/icons/search'\nimport Input from '@/src/shared/ui/input'\nimport { useSearchStore } from '../model/searchStore'\nimport style from './index.module.css'\nimport SearchDropdown from './searchDropdown'\n\nconst HeroSearch = () => {\n\tconst { searchQuery, setSearchQuery } = useSearchStore()\n\n\tconst handleSearch = (value: string) => {\n\t\tsetSearchQuery(value)\n\t\tconsole.log(value)\n\t}\n\n\treturn (\n\t\t<div className={style.search}>\n\t\t\t<Input\n\t\t\t\tplaceholder='Найти направление'\n\t\t\t\tprefix={<SearchIcon />}\n\t\t\t\tvalue={searchQuery}\n\t\t\t\tonChange={value => handleSearch(value.target.value)}\n\t\t\t/>\n\t\t\t<SearchDropdown searchQuery={searchQuery} />\n\t\t</div>\n\t)\n}\n\nexport default HeroSearch\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IAClB,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD;IAErD,MAAM,eAAe,CAAC;QACrB,eAAe;QACf,QAAQ,GAAG,CAAC;IACb;IAEA,qBACC,8OAAC;QAAI,WAAW,iJAAA,CAAA,UAAK,CAAC,MAAM;;0BAC3B,8OAAC,sIAAA,CAAA,UAAK;gBACL,aAAY;gBACZ,sBAAQ,8OAAC,uIAAA,CAAA,UAAU;;;;;gBACnB,OAAO;gBACP,UAAU,CAAA,QAAS,aAAa,MAAM,MAAM,CAAC,KAAK;;;;;;0BAEnD,8OAAC,+IAAA,CAAA,UAAc;gBAAC,aAAa;;;;;;;;;;;;AAGhC;uCAEe", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/icons/chevron.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\n\nconst ChevronIcon: FC = () => {\n\treturn (\n\t\t<Image src={'/chevron-icon.svg'} alt='Chevron' width={7} height={12} />\n\t)\n}\n\nexport default ChevronIcon\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,cAAkB;IACvB,qBACC,8OAAC,6HAAA,CAAA,UAAK;QAAC,KAAK;QAAqB,KAAI;QAAU,OAAO;QAAG,QAAQ;;;;;;AAEnE;uCAEe", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/entities/country/ui/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__M6SB_q__card\",\n  \"content\": \"index-module__M6SB_q__content\",\n  \"flagWrapper\": \"index-module__M6SB_q__flagWrapper\",\n  \"info\": \"index-module__M6SB_q__info\",\n  \"price\": \"index-module__M6SB_q__price\",\n  \"title\": \"index-module__M6SB_q__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/entities/country/ui/card.tsx"], "sourcesContent": ["'use client'\n\nimport ChevronIcon from '@/src/shared/ui/icons/chevron'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { FC } from 'react'\nimport { CountryI } from '../model/country'\nimport style from './index.module.css'\n\ninterface CountryCardProps {\n    country: CountryI\n}\n\nconst CountryCard: FC<CountryCardProps> = ({ country }) => {\n    if (country.url) {\n        const href = country.url;\n        return (\n            <Link href={href} className={style.card}>\n                <div className={style.content}>\n                    <div className={style.flagWrapper}>\n                        <Image src={`/flags/${country.iso?.toLowerCase()}.svg`} alt={country.iso ?? 'country iso'} width={32} height={32} />\n                    </div>\n                    <div className={style.info}>\n                        <span className={style.title}>{country.country}</span>\n                        <span className={style.price}>\n                            {country.classic_info?.price_per_gb + ' ₽/ГБ'}\n                        </span>\n                    </div>\n                </div>\n                <ChevronIcon />\n            </Link>\n        )\n    } else {\n        return null\n    }\n}\n\nexport default CountryCard\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AAPA;;;;;;AAaA,MAAM,cAAoC,CAAC,EAAE,OAAO,EAAE;IAClD,IAAI,QAAQ,GAAG,EAAE;QACb,MAAM,OAAO,QAAQ,GAAG;QACxB,qBACI,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM;YAAM,WAAW,qJAAA,CAAA,UAAK,CAAC,IAAI;;8BACnC,8OAAC;oBAAI,WAAW,qJAAA,CAAA,UAAK,CAAC,OAAO;;sCACzB,8OAAC;4BAAI,WAAW,qJAAA,CAAA,UAAK,CAAC,WAAW;sCAC7B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,GAAG,EAAE,cAAc,IAAI,CAAC;gCAAE,KAAK,QAAQ,GAAG,IAAI;gCAAe,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAElH,8OAAC;4BAAI,WAAW,qJAAA,CAAA,UAAK,CAAC,IAAI;;8CACtB,8OAAC;oCAAK,WAAW,qJAAA,CAAA,UAAK,CAAC,KAAK;8CAAG,QAAQ,OAAO;;;;;;8CAC9C,8OAAC;oCAAK,WAAW,qJAAA,CAAA,UAAK,CAAC,KAAK;8CACvB,QAAQ,YAAY,EAAE,eAAe;;;;;;;;;;;;;;;;;;8BAIlD,8OAAC,wIAAA,CAAA,UAAW;;;;;;;;;;;IAGxB,OAAO;QACH,OAAO;IACX;AACJ;uCAEe", "debugId": null}}]}