{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/entities/country/api/index.ts"], "sourcesContent": ["import { CountriesI } from '../model/country'\n\nexport class CountryApi {\n\tasync fetchCountries(): Promise<CountriesI> {\n\t\tconst res = await fetch(\n\t\t\t'https://api3.yesim.cc/sale_list?force_type=countries&lang=ru'\n\t\t)\n\t\tconst data: CountriesI = await res.json()\n\t\treturn data\n\t}\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACZ,MAAM,iBAAsC;QAC3C,MAAM,MAAM,MAAM,MACjB;QAED,MAAM,OAAmB,MAAM,IAAI,IAAI;QACvC,OAAO;IACR;AACD", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/logo/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"logo\": \"index-module__ytjyqG__logo\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/shared/ui/logo/index.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\ninterface LogoProps {\n\twithType?: boolean\n}\n\nconst Logo: FC<LogoProps> = ({ withType = true }) => {\n\treturn (\n\t\t<div className={style.logo}>\n\t\t\t<Image\n\t\t\t\tsrc={'/yesim_circle.svg'}\n\t\t\t\talt='Logo'\n\t\t\t\twidth={30}\n\t\t\t\theight={30}\n\t\t\t/>\n\t\t\t{withType && (\n\t\t\t\t<Image\n\t\t\t\t\tsrc={'/yesim_type.svg'}\n\t\t\t\t\talt='Logo'\n\t\t\t\t\twidth={65}\n\t\t\t\t\theight={23}\n\t\t\t\t/>\n\t\t\t)}\n\t\t</div>\n\t)\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAMA,MAAM,OAAsB,CAAC,EAAE,WAAW,IAAI,EAAE;IAC/C,qBACC,8OAAC;QAAI,WAAW,gJAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;YAER,0BACA,8OAAC,6HAAA,CAAA,UAAK;gBACL,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;;;;;;;AAKb;uCAEe", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/header/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"header\": \"index-module__6gvmdq__header\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/header/ui/index.tsx"], "sourcesContent": ["import Logo from '@/src/shared/ui/logo'\nimport Link from 'next/link'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\nconst Header: FC = () => {\n\treturn (\n\t\t<header className={style.header}>\n\t\t\t<Link href={'/'}>\n\t\t\t\t<Logo />\n\t\t\t</Link>\n\t\t</header>\n\t)\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,SAAa;IAClB,qBACC,8OAAC;QAAO,WAAW,mJAAA,CAAA,UAAK,CAAC,MAAM;kBAC9B,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM;sBACX,cAAA,8OAAC,qIAAA,CAAA,UAAI;;;;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/howItWorks/model/steps.ts"], "sourcesContent": ["interface StepI {\n    title: string\n    description?: string\n    image?: string\n}\n\nexport const steps: StepI[] = [\n    {\n        title: 'Очень длинный заголовок',\n    },\n    {\n        title: 'Определяем страну',\n        description:\n            'Вам нужно определить страну, в которой вы находитесь. Это можно сделать, выбрав страну из списка или введя ее в поиске.',\n    },\n    {\n        title: 'Выбираем тариф',\n        description:\n            'Выберите подходящий тариф. Мы предлагаем разные варианты, включая тарифы с ежедневной оплатой и тарифы с оплатой GB.',\n    },\n    {\n        title: 'Оплачиваим',\n        description:\n            'После выбора тарифа, перейдите к оплате. Мы принимаем платежи через банковскую карту, Apple Pay и Google Pay.',\n    },\n    {\n        title: 'Получаем eSIM',\n        description: 'После оплаты, вы получите eSIM.',\n    },\n]\n"], "names": [], "mappings": ";;;AAMO,MAAM,QAAiB;IAC1B;QACI,OAAO;IACX;IACA;QACI,OAAO;QACP,aACI;IACR;IACA;QACI,OAAO;QACP,aACI;IACR;IACA;QACI,OAAO;QACP,aACI;IACR;IACA;QACI,OAAO;QACP,aAAa;IACjB;CACH", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/widgets/howItWorks/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"card\": \"index-module__aAZmyW__card\",\n  \"dragging\": \"index-module__aAZmyW__dragging\",\n  \"step\": \"index-module__aAZmyW__step\",\n  \"stepImage\": \"index-module__aAZmyW__stepImage\",\n  \"stepTitle\": \"index-module__aAZmyW__stepTitle\",\n  \"steps\": \"index-module__aAZmyW__steps\",\n  \"title\": \"index-module__aAZmyW__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/widgets/howItWorks/ui/index.tsx"], "sourcesContent": ["import { FC, useCallback, useRef, useState } from 'react'\nimport { steps } from '../model/steps'\nimport style from './index.module.css'\n\nconst HowItsWork: FC = () => {\n\tconst scrollContainerRef = useRef<HTMLDivElement>(null)\n\tconst [isDragging, setIsDragging] = useState(false)\n\tconst [startX, setStartX] = useState(0)\n\tconst [scrollLeft, setScrollLeft] = useState(0)\n\n\tconst handleMouseDown = useCallback((e: React.MouseEvent) => {\n\t\tif (!scrollContainerRef.current) return\n\n\t\tsetIsDragging(true)\n\t\tsetStartX(e.pageX - scrollContainerRef.current.offsetLeft)\n\t\tsetScrollLeft(scrollContainerRef.current.scrollLeft)\n\n\t\t// Предотвращаем выделение текста при перетаскивании\n\t\te.preventDefault()\n\t}, [])\n\n\tconst handleMouseMove = useCallback((e: React.MouseEvent) => {\n\t\tif (!isDragging || !scrollContainerRef.current) return\n\n\t\te.preventDefault()\n\t\tconst x = e.pageX - scrollContainerRef.current.offsetLeft\n\t\tconst walk = (x - startX) * 2 // Множитель для скорости прокрутки\n\t\tscrollContainerRef.current.scrollLeft = scrollLeft - walk\n\t}, [isDragging, startX, scrollLeft])\n\n\tconst handleMouseUp = useCallback(() => {\n\t\tsetIsDragging(false)\n\t}, [])\n\n\tconst handleMouseLeave = useCallback(() => {\n\t\tsetIsDragging(false)\n\t}, [])\n\n\t// Touch события для мобильных устройств\n\tconst handleTouchStart = useCallback((e: React.TouchEvent) => {\n\t\tif (!scrollContainerRef.current) return\n\n\t\tsetIsDragging(true)\n\t\tsetStartX(e.touches[0].pageX - scrollContainerRef.current.offsetLeft)\n\t\tsetScrollLeft(scrollContainerRef.current.scrollLeft)\n\t}, [])\n\n\tconst handleTouchMove = useCallback((e: React.TouchEvent) => {\n\t\tif (!isDragging || !scrollContainerRef.current) return\n\n\t\tconst x = e.touches[0].pageX - scrollContainerRef.current.offsetLeft\n\t\tconst walk = (x - startX) * 2\n\t\tscrollContainerRef.current.scrollLeft = scrollLeft - walk\n\t}, [isDragging, startX, scrollLeft])\n\n\tconst handleTouchEnd = useCallback(() => {\n\t\tsetIsDragging(false)\n\t}, [])\n\n\treturn (\n\t\t<div className={style.card}>\n\t\t\t<h2 className={style.title}>Как это работает</h2>\n\t\t\t<div\n\t\t\t\tref={scrollContainerRef}\n\t\t\t\tclassName={`${style.steps} ${isDragging ? style.dragging : ''}`}\n\t\t\t\tonMouseDown={handleMouseDown}\n\t\t\t\tonMouseMove={handleMouseMove}\n\t\t\t\tonMouseUp={handleMouseUp}\n\t\t\t\tonMouseLeave={handleMouseLeave}\n\t\t\t\tonTouchStart={handleTouchStart}\n\t\t\t\tonTouchMove={handleTouchMove}\n\t\t\t\tonTouchEnd={handleTouchEnd}\n\t\t\t>\n\t\t\t\t{steps.map((step) => {\n\t\t\t\t\treturn (\n\t\t\t\t\t\t<div key={step.title} className={style.step}>\n\t\t\t\t\t\t\t<h3 className={style.stepTitle}>\n\t\t\t\t\t\t\t\t{step.title}\n\t\t\t\t\t\t\t</h3>\n\t\t\t\t\t\t\t<div className={style.stepImage} />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t)\n\t\t\t\t})}\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default HowItsWork\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,aAAiB;IACtB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,cAAc;QACd,UAAU,EAAE,KAAK,GAAG,mBAAmB,OAAO,CAAC,UAAU;QACzD,cAAc,mBAAmB,OAAO,CAAC,UAAU;QAEnD,oDAAoD;QACpD,EAAE,cAAc;IACjB,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,CAAC,cAAc,CAAC,mBAAmB,OAAO,EAAE;QAEhD,EAAE,cAAc;QAChB,MAAM,IAAI,EAAE,KAAK,GAAG,mBAAmB,OAAO,CAAC,UAAU;QACzD,MAAM,OAAO,CAAC,IAAI,MAAM,IAAI,EAAE,mCAAmC;;QACjE,mBAAmB,OAAO,CAAC,UAAU,GAAG,aAAa;IACtD,GAAG;QAAC;QAAY;QAAQ;KAAW;IAEnC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,cAAc;IACf,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,cAAc;IACf,GAAG,EAAE;IAEL,wCAAwC;IACxC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,cAAc;QACd,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG,mBAAmB,OAAO,CAAC,UAAU;QACpE,cAAc,mBAAmB,OAAO,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,CAAC,cAAc,CAAC,mBAAmB,OAAO,EAAE;QAEhD,MAAM,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG,mBAAmB,OAAO,CAAC,UAAU;QACpE,MAAM,OAAO,CAAC,IAAI,MAAM,IAAI;QAC5B,mBAAmB,OAAO,CAAC,UAAU,GAAG,aAAa;IACtD,GAAG;QAAC;QAAY;QAAQ;KAAW;IAEnC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,cAAc;IACf,GAAG,EAAE;IAEL,qBACC,8OAAC;QAAI,WAAW,uJAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC;gBAAG,WAAW,uJAAA,CAAA,UAAK,CAAC,KAAK;0BAAE;;;;;;0BAC5B,8OAAC;gBACA,KAAK;gBACL,WAAW,GAAG,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,uJAAA,CAAA,UAAK,CAAC,QAAQ,GAAG,IAAI;gBAC/D,aAAa;gBACb,aAAa;gBACb,WAAW;gBACX,cAAc;gBACd,cAAc;gBACd,aAAa;gBACb,YAAY;0BAEX,8IAAA,CAAA,QAAK,CAAC,GAAG,CAAC,CAAC;oBACX,qBACC,8OAAC;wBAAqB,WAAW,uJAAA,CAAA,UAAK,CAAC,IAAI;;0CAC1C,8OAAC;gCAAG,WAAW,uJAAA,CAAA,UAAK,CAAC,SAAS;0CAC5B,KAAK,KAAK;;;;;;0CAEZ,8OAAC;gCAAI,WAAW,uJAAA,CAAA,UAAK,CAAC,SAAS;;;;;;;uBAJtB,KAAK,KAAK;;;;;gBAOtB;;;;;;;;;;;;AAIJ;uCAEe", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/pages/country/ui/index.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"content\": \"index-module__C7fkEW__content\",\n  \"countryDescription\": \"index-module__C7fkEW__countryDescription\",\n  \"countryHeader\": \"index-module__C7fkEW__countryHeader\",\n  \"countryTitle\": \"index-module__C7fkEW__countryTitle\",\n  \"flagWrapper\": \"index-module__C7fkEW__flagWrapper\",\n  \"main\": \"index-module__C7fkEW__main\",\n  \"page\": \"index-module__C7fkEW__page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/src/pages/country/ui/index.tsx"], "sourcesContent": ["import { CountryI } from '@/src/entities/country/model/country'\nimport Header from '@/src/widgets/header/ui'\nimport HowItsWork from '@/src/widgets/howItWorks/ui'\nimport Image from 'next/image'\nimport { FC } from 'react'\nimport style from './index.module.css'\n\ninterface CountryPageProps {\n\tcountry: CountryI\n}\n\nconst CountryPage: FC<CountryPageProps> = ({ country }) => {\n\treturn (\n\t\t<div className={style.page}>\n\t\t\t<Header />\n\t\t\t<div className={style.content}>\n\t\t\t\t<main className={style.main}>\n\t\t\t\t\t<div className={style.countryHeader}>\n\t\t\t\t\t\t<div className={style.countryInfo}>\n\t\t\t\t\t\t\t<h1 className={style.countryTitle}>{country.country}</h1>\n\t\t\t\t\t\t\t<p className={style.countryDescription}>Туристические SIM-карты с интернетом</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className={style.flagWrapper}>\n\t\t\t\t\t\t\t<Image src={`/flags/${country.iso?.toLowerCase()}.svg`} alt={country.iso ?? 'country iso'} width={32} height={32} />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<HowItsWork />\n\t\t\t\t</main>\n\t\t\t</div>\n\t\t</div>\n\t)\n}\n\nexport default CountryPage\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;;;;;;AAMA,MAAM,cAAoC,CAAC,EAAE,OAAO,EAAE;IACrD,qBACC,8OAAC;QAAI,WAAW,kJAAA,CAAA,UAAK,CAAC,IAAI;;0BACzB,8OAAC,wIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAW,kJAAA,CAAA,UAAK,CAAC,OAAO;0BAC5B,cAAA,8OAAC;oBAAK,WAAW,kJAAA,CAAA,UAAK,CAAC,IAAI;;sCAC1B,8OAAC;4BAAI,WAAW,kJAAA,CAAA,UAAK,CAAC,aAAa;;8CAClC,8OAAC;oCAAI,WAAW,kJAAA,CAAA,UAAK,CAAC,WAAW;;sDAChC,8OAAC;4CAAG,WAAW,kJAAA,CAAA,UAAK,CAAC,YAAY;sDAAG,QAAQ,OAAO;;;;;;sDACnD,8OAAC;4CAAE,WAAW,kJAAA,CAAA,UAAK,CAAC,kBAAkB;sDAAE;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAW,kJAAA,CAAA,UAAK,CAAC,WAAW;8CAChC,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,GAAG,EAAE,cAAc,IAAI,CAAC;wCAAE,KAAK,QAAQ,GAAG,IAAI;wCAAe,OAAO;wCAAI,QAAQ;;;;;;;;;;;;;;;;;sCAGhH,8OAAC,4IAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;AAKhB;uCAEe", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/develop/githubRepositories/yesim-test-app/app/country/%5BcountryName%5D/page.tsx"], "sourcesContent": ["import { CountryApi } from '@/src/entities/country/api'\nimport CountryPage from '@/src/pages/country/ui'\nimport { notFound } from 'next/navigation'\nimport { FC } from 'react'\n\ninterface CountryPageProps {\n\tparams: Promise<{\n\t\tcountryName: string\n\t}>\n}\n\nasync function findCountry(url: string) {\n\tconst countryApi = new CountryApi()\n\tconst countries = await countryApi.fetchCountries()\n\n\tfor (const c of Object.values(countries.countries || {})) {\n\t\tconst country = c.find((c) => {\n\t\t\tconsole.log(c.url, url)\n\t\t\treturn c.url === url\n\t\t})\n\t\tif (country) {\n\t\t\treturn country\n\t\t}\n\t}\n\n\treturn null\n}\n\nconst CountryPageRoute: FC<CountryPageProps> = async ({ params }) => {\n\tconst { countryName } = await params\n\n\tconst foundCountry = await findCountry(`/country/${countryName}/`)\n\tif (!foundCountry) {\n\t\tnotFound()\n\t}\n\n\treturn <CountryPage country={foundCountry} />\n}\n\nexport default CountryPageRoute\n\n// seo\nexport async function generateMetadata({ params }: CountryPageProps) {\n\tconst { countryName } = await params\n\tconst foundCountry = await findCountry(`/country/${countryName}/`)\n\tif (!foundCountry) {\n\t\treturn {\n\t\t\ttitle: 'Страна не найдена'\n\t\t}\n\t}\n\tconst priceText = foundCountry.classic_info?.price_per_gb\n\t\t? ` от ${foundCountry.classic_info.price_per_gb} ₽/ГБ`\n\t\t: ''\n\treturn {\n\t\ttitle: `${foundCountry.country} - eSIM тарифы${priceText}`,\n\t\tdescription: `Купить eSIM для ${foundCountry.country}. ${foundCountry.classic_info?.price_per_gb ? `Цена от ${foundCountry.classic_info.price_per_gb} ₽/ГБ.` : ''} Быстрая активация и надежная связь.`\n\t}\n}\n\n// generate static pages\nexport async function generateStaticParams() {\n\tconst countryApi = new CountryApi()\n\tconst countries = await countryApi.fetchCountries()\n\tconst slugs: string[] = []\n\tObject.values(countries.countries || {}).forEach((countryGroup) => {\n\t\tcountryGroup.forEach((country) => {\n\t\t\tif (country.url) {\n\t\t\t\tconst slug = country.url.includes('/') ? country.url.split('/').pop() : country.url\n\t\t\t\tif (slug && !slugs.includes(slug)) {\n\t\t\t\t\tslugs.push(slug)\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t})\n\treturn slugs.map((slug) => ({\n\t\tcountryName: slug\n\t}))\n}"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;;;;;AASA,eAAe,YAAY,GAAW;IACrC,MAAM,aAAa,IAAI,0IAAA,CAAA,aAAU;IACjC,MAAM,YAAY,MAAM,WAAW,cAAc;IAEjD,KAAK,MAAM,KAAK,OAAO,MAAM,CAAC,UAAU,SAAS,IAAI,CAAC,GAAI;QACzD,MAAM,UAAU,EAAE,IAAI,CAAC,CAAC;YACvB,QAAQ,GAAG,CAAC,EAAE,GAAG,EAAE;YACnB,OAAO,EAAE,GAAG,KAAK;QAClB;QACA,IAAI,SAAS;YACZ,OAAO;QACR;IACD;IAEA,OAAO;AACR;AAEA,MAAM,mBAAyC,OAAO,EAAE,MAAM,EAAE;IAC/D,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM;IAE9B,MAAM,eAAe,MAAM,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IACjE,IAAI,CAAC,cAAc;QAClB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACR;IAEA,qBAAO,8OAAC,uIAAA,CAAA,UAAW;QAAC,SAAS;;;;;;AAC9B;uCAEe;AAGR,eAAe,iBAAiB,EAAE,MAAM,EAAoB;IAClE,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM;IAC9B,MAAM,eAAe,MAAM,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IACjE,IAAI,CAAC,cAAc;QAClB,OAAO;YACN,OAAO;QACR;IACD;IACA,MAAM,YAAY,aAAa,YAAY,EAAE,eAC1C,CAAC,IAAI,EAAE,aAAa,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,GACpD;IACH,OAAO;QACN,OAAO,GAAG,aAAa,OAAO,CAAC,cAAc,EAAE,WAAW;QAC1D,aAAa,CAAC,gBAAgB,EAAE,aAAa,OAAO,CAAC,EAAE,EAAE,aAAa,YAAY,EAAE,eAAe,CAAC,QAAQ,EAAE,aAAa,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,oCAAoC,CAAC;IACxM;AACD;AAGO,eAAe;IACrB,MAAM,aAAa,IAAI,0IAAA,CAAA,aAAU;IACjC,MAAM,YAAY,MAAM,WAAW,cAAc;IACjD,MAAM,QAAkB,EAAE;IAC1B,OAAO,MAAM,CAAC,UAAU,SAAS,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;QACjD,aAAa,OAAO,CAAC,CAAC;YACrB,IAAI,QAAQ,GAAG,EAAE;gBAChB,MAAM,OAAO,QAAQ,GAAG,CAAC,QAAQ,CAAC,OAAO,QAAQ,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,QAAQ,GAAG;gBACnF,IAAI,QAAQ,CAAC,MAAM,QAAQ,CAAC,OAAO;oBAClC,MAAM,IAAI,CAAC;gBACZ;YACD;QACD;IACD;IACA,OAAO,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;YAC3B,aAAa;QACd,CAAC;AACF", "debugId": null}}]}