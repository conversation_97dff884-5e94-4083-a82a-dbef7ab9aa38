.page {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	padding: 0 0.5rem;
}

.main {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
	padding: 0.5rem 0;

	@media (min-width: 768px) {
		padding: 3.25rem 0;
		gap: 1.5rem;
	}
}

.content {
	width: 100%;
	max-width: 720px;
	padding: 4rem 0 0 0;
	margin: 0 auto;
}

.countryHeader {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 0.5rem;
	padding: 1rem;
	background: var(--surface);
	border-radius: 1.5rem;
}

.flagWrapper {
	width: 2rem;
	height: 2rem;
	border-radius: 50%;
	background: var(--background);
	overflow: hidden;
}

.countryTitle {
	font-size: 2rem;
	font-weight: 600;
	margin: 0 0 0.5rem 0;
	color: var(--text-primary);
}

.countryDescription {
	font-size: 0.875rem;
	font-weight: var(--font-body-weight);
	line-height: var(--font-body-line-height);
	letter-spacing: var(--font-body-letter-spacing);
}