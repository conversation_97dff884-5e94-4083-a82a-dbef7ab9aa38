.card {
	display: flex;
	flex-direction: column;
	gap: 1rem;
	background: var(--surface);
	border-radius: 1.25rem;

	overflow: hidden;
}

.title {
	font-size: var(--font-subtitle-size);
	font-weight: var(--font-subtitle-weight);
	line-height: var(--font-subtitle-line-height);
	letter-spacing: var(--font-subtitle-letter-spacing);
	padding: 1rem;
}

.steps {
	display: flex;
	gap: 0.375rem;
	width: 100%;
	overflow-x: auto;
	overflow-y: hidden;
	scrollbar-width: none;
	-ms-overflow-style: none;
	padding: 1rem;
}

.step {
	width: 15.625rem;
	min-width: 15.625rem;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
	gap: 0.5rem;
	background: var(--background);
	border-radius: 1.25rem;
}

.stepTitle {
	font-size: var(--font-body-size);
	font-weight: 600;
	line-height: var(--font-body-line-height);
	letter-spacing: var(--font-body-letter-spacing);
	text-align: center;
	padding: 1rem 1.5rem;
}

.stepImage {
	width: 10.125rem;
	height: 15.375rem;
	border-radius: 1.5rem 1.5rem 0 0;
	background: #D9D9D9;
}